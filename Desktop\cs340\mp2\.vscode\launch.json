{"configurations": [{"name": "Debug with Makefile + GDB (one argument)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/dll_c", "args": ["${input:arg1}"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "setupCommands": [{"description": "Enable pretty-printing in GDB", "text": "--enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "Makefile build"}, {"name": "Debug with Makefile + GDB (three arguments)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/dll_c", "args": ["${input:arg1}", "${input:arg2}", "${input:arg3}"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "setupCommands": [{"description": "Enable pretty-printing in GDB", "text": "--enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "Makefile build"}, {"type": "cppdbg", "name": "Test with <PERSON><PERSON><PERSON>", "request": "launch", "program": "/usr/bin/make", "args": ["test"], "cwd": "${workspaceFolder}", "preLaunchTask": "Makefile build"}], "inputs": [{"id": "arg1", "type": "promptString", "description": "First argument to provide on command line", "default": "one"}, {"id": "arg2", "type": "promptString", "description": "Second argument to provide on command line", "default": "two"}, {"id": "arg3", "type": "promptString", "description": "Third argument to provide on command line", "default": "three"}], "version": "0.2.0"}