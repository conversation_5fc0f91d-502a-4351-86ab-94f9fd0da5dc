#include "dll.h"
#include <string.h>
#include <stdlib.h>

// Node detach functions
void dllDetachc(dllNodec *self) {
    // Removes this node from its list
    if (self == NULL) return;

    if (self->prev != NULL) {
        self->prev->next = self->next;
    }
    if (self->next != NULL) {
        self->next->prev = self->prev;
    }

    self->prev = NULL;
    self->next = NULL;
}

void dllDetachs(dllNodes *self) {
    // Removes this node from its list for string
    if (self == NULL) return;

    if (self->prev != NULL) {
        self->prev->next = self->next;
    }
    if (self->next != NULL) {
        self->next->prev = self->prev;
    }

    self->prev = NULL;
    self->next = NULL;
}

// List()
void dllInitc(dllListc *self) {
    // Set head and tail to NULL
    if (self == NULL) return;
    self->head = NULL;
    self->tail = NULL;
}

void dllInits(dllLists *self) {
    // Set head and tail to NULL
    if (self == NULL) return;
    self->head = NULL;
    self->tail = NULL;
}

// List cleanup functions
void dllClearc(dllListc *self) {
    if (self == NULL) return;

    dllNodec *current = self->head;
    while (current != NULL) {
        dllNodec *next = current->next;
        free(current);
        current = next;
    }

    self->head = NULL;
    self->tail = NULL;
}

void dllClears(dllLists *self) {
    if (self == NULL) return;

    dllNodes *current = self->head;
    while (current != NULL) {
        dllNodes *next = current->next;
        if (current->data != NULL) {
            free(current->data);
        }
        free(current);
        current = next;
    }

    self->head = NULL;
    self->tail = NULL;
}

// Find functions
dllNodec *dllFindc(const dllListc *self, const char value) {
    if (self == NULL) return NULL;

    dllNodec *current = self->head;
    while (current != NULL) {
        if (current->data == value) {
            return current;
        }
        current = current->next;
    }

    return NULL;
}

dllNodes *dllFinds(const dllLists *self, const char *value) {
    if (self == NULL || value == NULL) return NULL;

    dllNodes *current = self->head;
    while (current != NULL) {
        if (current->data != NULL && strcmp(current->data, value) == 0) {
            return current;
        }
        current = current->next;
    }

    return NULL;
}

// Pop functions (remove from head)
char dllPopc(dllListc *self, char ifEmpty) {
    if (self == NULL || self->head == NULL) {
        return ifEmpty;
    }

    dllNodec *node = self->head;
    self->head = node->next;
    if (node->next == NULL) {
        self->head = NULL;
        self->tail = NULL;
    }
    dllDetachc(node);
    char data = node->data;
    free(node);
    return data;
}

char *dllPops(dllLists *self, char *ifEmpty) {
    if (self == NULL || self->head == NULL) {
        return ifEmpty;
    }

    dllNodes *node = self->head;
    self->head = node->next;
    if (node->next == NULL) {
        self->head = NULL;
        self->tail = NULL;
    }
    dllDetachs(node);
    char* data = node->data;
    free(node);
    return data;
}

// Shift functions (remove from tail)
char dllShiftc(dllListc *self, char ifEmpty) {
    if (self == NULL || self->tail == NULL) {
        return ifEmpty;
    }
    dllNodec *node = self->tail;
    if (node->prev == NULL) {
        self->head = NULL;
        self->tail = NULL;
    }
    self->tail = node->prev;
    dllDetachc(node);
    char data = node->data;
    free(node);
    return data;
}

char *dllShifts(dllLists *self, char *ifEmpty) {
    if (self == NULL || self->tail == NULL) {
        return ifEmpty;
    }

    dllNodes *node = self->tail;
    self->tail = node->prev;
    if (node->prev == NULL) {
        self->head = NULL;
        self->tail = NULL;
    }
    self->tail = node->prev;
    dllDetachs(node);
    char* data = node->data;
    free(node);
    return data;
}

// Push functions (add to head)
void dllPushc(dllListc *self, char value) {
    if (self == NULL) return;

    dllNodec *newNode = (dllNodec*)malloc(sizeof(dllNodec));
    if (newNode == NULL) return;

    newNode->data = value;
    newNode->next = self->head;
    newNode->prev = NULL;

    if (self->head == NULL) {
        self->head->prev = newNode;
    }
    self->head = newNode;
    if (self->tail == NULL) {
        self->tail = self->head;
    }
}

void dllPushs(dllLists *self, char *value) {
    if (self == NULL || value == NULL) return;

    dllNodes *newNode = (dllNodes*)malloc(sizeof(dllNodes));
    if (newNode == NULL) return;

    newNode->data = (char*)malloc(strlen(value) + 1);
    if (newNode->data == NULL) {
        free(newNode);
        return;
    }
    strcpy(newNode->data, value);

    newNode->next = self->head;
    newNode->prev = NULL;

    if (self->head != NULL) {
        self->head->prev = newNode;
    }
    self->head = newNode;
    if (self->tail == NULL) {
        self->tail = self->head;
    }
}

// Enqueue functions (add to tail)
void dllEnqueuec(dllListc *self, char value) {
    if (self == NULL) return;

    dllNodec *newNode = (dllNodec*)malloc(sizeof(dllNodec));
    if (newNode == NULL) return;

    newNode->data = value;
    newNode->next = NULL;
    newNode->prev = self->tail;

    if (self->tail != NULL) {
        self->tail->next = newNode;
    }
    self->tail = newNode;
    if (self->head == NULL) {
        self->head = self->tail;
    }
}

void dllEnqueues(dllLists *self, char *value) {
    if (self == NULL || value == NULL) return;

    dllNodes *newNode = (dllNodes*)malloc(sizeof(dllNodes));
    if (newNode == NULL) return;

    newNode->data = (char*)malloc(strlen(value) + 1);
    if (newNode->data == NULL) {
        free(newNode);
        return;
    }
    strcpy(newNode->data, value);

    newNode->next = NULL;
    newNode->prev = self->tail;

    if (self->tail != NULL) {
        self->tail->next = newNode;
    }
    self->tail = newNode;
    if (self->head == NULL) {
        self->head = self->tail;
    }
}